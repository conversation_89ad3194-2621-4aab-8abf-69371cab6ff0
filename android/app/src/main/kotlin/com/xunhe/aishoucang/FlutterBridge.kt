package com.xunhe.aishoucang

import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import android.view.WindowManager
import android.widget.Toast
import java.io.File
import com.xunhe.aishoucang.lib.AccessibilityHelper
import com.xunhe.aishoucang.lib.ClipboardHelper
import com.xunhe.aishoucang.lib.CustomNotificationPanel
import com.xunhe.aishoucang.lib.FFmpegHelper
import com.xunhe.aishoucang.lib.FloatingWindowHelper
import com.xunhe.aishoucang.lib.PaddleOCRHelper
import com.xunhe.aishoucang.lib.ReturnButtonHelper
import com.xunhe.aishoucang.lib.SharedPreferencesHelper
import com.xunhe.aishoucang.lib.SidebarHelper
import com.xunhe.aishoucang.lib.SidebarFloatingMenuHelper
import com.xunhe.aishoucang.lib.ToastHelper
import com.xunhe.aishoucang.lib.WebViewHelper
import com.xunhe.aishoucang.lib.NoteWebviewHelper
import com.xunhe.aishoucang.helpers.CustomToastHelper
import com.xunhe.aishoucang.helpers.CustomToastModalHelper
import com.xunhe.aishoucang.helpers.OssResourceBridge
import com.xunhe.aishoucang.helpers.OssBridge
import com.xunhe.aishoucang.helpers.ConfigHelper
import com.xunhe.aishoucang.helpers.WebViewHtmlExtractor
import com.xunhe.aishoucang.helpers.WechatHelper
import com.xunhe.aishoucang.helpers.WechatAuthListener
import com.xunhe.aishoucang.helpers.WechatShareListener
import com.xunhe.aishoucang.helpers.SparkChainHelper
import com.xunhe.aishoucang.helpers.TencentAsrHelper
import com.xunhe.aishoucang.services.AppDownloadManager

import com.xunhe.aishoucang.api.NoteApi
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.EventChannel
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result

/**
 * Flutter桥接类
 * 负责处理Flutter和原生代码之间的通信
 */
class FlutterBridge(private val context: Context) : MethodCallHandler {
    companion object {
        private const val TAG = "FlutterBridge"
        private const val CHANNEL = "com.xunhe.aishoucang/method_channel"
        private const val CLIPBOARD_CHANNEL = "com.xunhe.aishoucang/clipboard"
        private const val EVENT_CHANNEL = "com.xunhe.aishoucang/clipboard_events"
        private const val FLOATING_WINDOW_EVENT_CHANNEL = "com.xunhe.aishoucang/floating_window_events"
        private const val WECHAT_EVENT_CHANNEL = "com.xunhe.aishoucang/wechat_events"
        private const val PROGRESS_EVENT_CHANNEL = "com.xunhe.aishoucang/progress_events"

        // 进度事件Sink
        var progressEventSink: EventChannel.EventSink? = null

        // 单例实例
        @Volatile
        private var instance: FlutterBridge? = null

        fun getInstance(context: Context): FlutterBridge {
            return instance ?: synchronized(this) {
                instance ?: FlutterBridge(context.applicationContext).also { instance = it }
            }
        }

        fun setup(flutterEngine: FlutterEngine, context: Context) {
            val bridge = getInstance(context)

            // 设置主方法通道
            val methodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL)
            methodChannel.setMethodCallHandler(bridge)
            bridge.methodChannel = methodChannel

            // 设置剪贴板方法通道
            val clipboardChannel =
                MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CLIPBOARD_CHANNEL)
            clipboardChannel.setMethodCallHandler(bridge)

            // 设置剪贴板事件通道
            val eventChannel =
                EventChannel(flutterEngine.dartExecutor.binaryMessenger, EVENT_CHANNEL)
            eventChannel.setStreamHandler(ClipboardEventHandler(context))

            // 设置悬浮窗事件通道
            val floatingWindowEventChannel =
                EventChannel(flutterEngine.dartExecutor.binaryMessenger, FLOATING_WINDOW_EVENT_CHANNEL)
            floatingWindowEventChannel.setStreamHandler(FloatingWindowEventHandler(context))

            // 设置微信事件通道
            val wechatEventChannel =
                EventChannel(flutterEngine.dartExecutor.binaryMessenger, WECHAT_EVENT_CHANNEL)
            wechatEventChannel.setStreamHandler(WechatEventHandler(context))

            // 设置进度事件通道
            val progressEventChannel =
                EventChannel(flutterEngine.dartExecutor.binaryMessenger, PROGRESS_EVENT_CHANNEL)
            progressEventChannel.setStreamHandler(ProgressEventHandler())

        }
    }

    // 剪贴板助手
    private val clipboardHelper = ClipboardHelper.getInstance(context)

    // 无障碍服务助手
    private val accessibilityHelper = AccessibilityHelper.getInstance(context)

    // 悬浮窗助手
    private val floatingWindowHelper = FloatingWindowHelper.getInstance(context)

    // 侧边栏助手
    private val sidebarHelper = SidebarHelper.getInstance(context)

    // 侧边栏悬浮菜单助手
    private val sidebarFloatingMenuHelper = SidebarFloatingMenuHelper.getInstance(context)

    // 返回按钮悬浮窗助手
    private val returnButtonHelper = ReturnButtonHelper.getInstance(context)

    // FFmpeg助手
    private val ffmpegHelper = FFmpegHelper.getInstance(context)

    // OCR文字识别助手 (替换原MLKit为PaddleOCR)
    private val paddleOcrHelper = PaddleOCRHelper(context)

    // WebView助手
    private val webViewHelper = WebViewHelper.getInstance(context)

    // SharedPreferences助手
    private val sharedPreferencesHelper = SharedPreferencesHelper.getInstance(context)

    // 微信助手
    private val wechatHelper = WechatHelper.getInstance(context)

    // 应用下载管理器
    private val appDownloadManager = AppDownloadManager.getInstance(context)

    // 自定义通知面板
    private val customNotificationPanel = CustomNotificationPanel.getInstance(context)

    // 笔记WebView助手
    private val noteWebviewHelper = NoteWebviewHelper.getInstance(context)

    // SparkChain 大模型识别助手
    private val sparkChainHelper = SparkChainHelper.getInstance(context)

    // 腾讯云ASR助手
    private val tencentAsrHelper = TencentAsrHelper.getInstance(context)

    // 主方法通道，用于回调
    private var methodChannel: MethodChannel? = null

    /**
     * 检查是否有悬浮窗权限
     */
    private fun checkOverlayPermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(context)
        } else {
            true // 低于Android 6.0的设备默认拥有悬浮窗权限
        }
    }

    /**
     * 打开悬浮窗权限设置页面
     */
    private fun openOverlayPermissionSettings() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            context.startActivity(intent)
        }
    }

    /**
     * 处理来自Flutter的方法调用
     */
    override fun onMethodCall(call: MethodCall, result: Result) {
        Log.d(TAG, "收到Flutter方法调用: ${call.method}")

        try {
            when (call.method) {
                // 剪贴板相关方法
                "initClipboardMonitor" -> {
                    clipboardHelper.startMonitor()
                    result.success(true)
                }
                "startMonitor" -> {
                    clipboardHelper.startMonitor()
                    result.success(true)
                }
                "stopMonitor" -> {
                    clipboardHelper.stopMonitor()
                    result.success(true)
                }
                "getClipboardText" -> {
                    val text = clipboardHelper.getClipboardText()
                    result.success(text)
                }
                "setClipboardText" -> {
                    val text = call.argument<String>("text")
                    if (text != null) {
                        clipboardHelper.setClipboardText(text)
                        result.success(true)
                    } else {
                        result.error("INVALID_ARGUMENT", "文本不能为空", null)
                    }
                }

                // 悬浮窗相关方法
                "toggleFloatingWindow" -> {
                    val enabled = call.argument<Boolean>("enabled") ?: false
                    if (enabled) {
                        floatingWindowHelper.show()
                    } else {
                        floatingWindowHelper.hide()
                    }
                    result.success(true)
                }
                "showPanel" -> {
                    try {
                        val service = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
                        floatingWindowHelper.show()
                        result.success(true)
                    } catch (e: Exception) {
                        Log.e(TAG, "获取WindowManager服务失败", e)
                        result.error("SERVICE_ERROR", "无法获取WindowManager服务: ${e.message}", null)
                    }
                }
                "showFloatingWindow" -> {
                    val content = call.argument<String>("content")
                    floatingWindowHelper.showFloatingWindow(content)
                    result.success(true)
                }
                "hideFloatingWindow" -> {
                    floatingWindowHelper.hideFloatingWindow()
                    result.success(true)
                }
                "isFloatingWindowShowing" -> {
                    val isShowing = floatingWindowHelper.isFloatingWindowShowing()
                    result.success(isShowing)
                }
                "checkOverlayPermission" -> {
                    val hasPermission = checkOverlayPermission()
                    result.success(hasPermission)
                }
                "openOverlayPermissionSettings" -> {
                    openOverlayPermissionSettings()
                    result.success(true)
                }
                "enableFloatingWindow" -> {
                    val content = call.argument<String>("content")
                    val hasPermission = checkOverlayPermission()

                    if (hasPermission) {
                        floatingWindowHelper.showFloatingWindow(content)
                        result.success(true)
                    } else {
                        // 没有悬浮窗权限，返回失败
                        result.success(false)
                    }
                }

                // 侧边栏相关方法
                "showSidebar" -> {
                    val hasPermission = checkOverlayPermission()
                    if (hasPermission) {
                        sidebarHelper.show()
                        result.success(true)
                    } else {
                        result.success(false)
                    }
                }
                "hideSidebar" -> {
                    sidebarHelper.hide()
                    result.success(true)
                }
                "isSidebarShowing" -> {
                    val isShowing = sidebarHelper.isSidebarShowing()
                    result.success(isShowing)
                }
                "showSidebarAtPosition" -> {
                    val x = call.argument<Int>("x")
                    val y = call.argument<Int>("y")
                    val hasPermission = checkOverlayPermission()

                    if (hasPermission) {
                        sidebarHelper.showSidebarAtPosition(x, y)
                        result.success(true)
                    } else {
                        result.success(false)
                    }
                }
                "getSidebarPosition" -> {
                    val position = sidebarHelper.getSidebarPosition()
                    if (position != null) {
                        result.success(mapOf("x" to position.first, "y" to position.second))
                    } else {
                        result.success(null)
                    }
                }

                // 侧边栏悬浮菜单相关方法
                "showSidebarFloatingMenu" -> {
                    val hasPermission = checkOverlayPermission()
                    if (hasPermission) {
                        sidebarFloatingMenuHelper.show()
                        result.success(true)
                    } else {
                        result.success(false)
                    }
                }
                "hideSidebarFloatingMenu" -> {
                    sidebarFloatingMenuHelper.hide()
                    result.success(true)
                }
                "isSidebarFloatingMenuShowing" -> {
                    val isShowing = sidebarFloatingMenuHelper.isSidebarFloatingMenuShowing()
                    result.success(isShowing)
                }

                // 无障碍服务相关方法
                "isAccessibilityServiceEnabled" -> {
                    val isEnabled = accessibilityHelper.isAccessibilityServiceEnabled()
                    result.success(isEnabled)
                }
                "checkAccessibilityPermission" -> {
                    val isEnabled = accessibilityHelper.isAccessibilityServiceEnabled()
                    result.success(isEnabled)
                }
                "openAccessibilitySettings" -> {
                    accessibilityHelper.openAccessibilitySettings(context)
                    result.success(true)
                }
                "findAndClickElementById" -> {
                    val elementId = call.argument<String>("elementId")
                    if (elementId != null) {
                        val success = accessibilityHelper.findAndClickElementById(elementId)
                        result.success(success)
                    } else {
                        result.error("INVALID_ARGUMENT", "元素ID不能为空", null)
                    }
                }

                // 注意：已移除无障碍模式下监听剪贴板相关功能，因为无法跨APP收集

                // FFmpeg相关方法
                "downloadVideo" -> {
                    val url = call.argument<String>("url")
                    val userAgent = call.argument<String>("userAgent")
                    val referer = call.argument<String>("referer")
                    val outputPath = call.argument<String>("outputPath")
                    val saveToGallery = call.argument<Boolean>("saveToGallery") ?: true

                    if (url.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "视频URL不能为空", null)
                        return
                    }

                    ffmpegHelper.downloadVideo(
                        url = url,
                        userAgent = userAgent,
                        referer = referer,
                        outputPath = outputPath,
                        saveToGallery = saveToGallery
                    ) { success, filePath, errorMessage ->
                        if (success) {
                            result.success(mapOf(
                                "success" to true,
                                "filePath" to filePath
                            ))
                        } else {
                            result.success(mapOf(
                                "success" to false,
                                "error" to errorMessage
                            ))
                        }
                    }
                }
                "convertVideoToAudio" -> {
                    val videoPath = call.argument<String>("videoPath")
                    val outputPath = call.argument<String>("outputPath")
                    val audioCodec = call.argument<String>("audioCodec") ?: "libmp3lame"
                    val audioBitrate = call.argument<String>("audioBitrate") ?: "192k"
                    val saveToGallery = call.argument<Boolean>("saveToGallery") ?: true

                    if (videoPath.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "视频文件路径不能为空", null)
                        return
                    }

                    ffmpegHelper.convertVideoToAudio(
                        videoPath = videoPath,
                        outputPath = outputPath,
                        audioCodec = audioCodec,
                        audioBitrate = audioBitrate,
                        saveToGallery = saveToGallery
                    ) { success, filePath, errorMessage ->
                        if (success) {
                            result.success(mapOf(
                                "success" to true,
                                "filePath" to filePath
                            ))
                        } else {
                            result.success(mapOf(
                                "success" to false,
                                "error" to errorMessage
                            ))
                        }
                    }
                }
                "cancelFFmpegTasks" -> {
                    ffmpegHelper.cancelAllTasks()
                    result.success(true)
                }

                "splitAudio" -> {
                    val audioPath = call.argument<String>("audioPath")
                    val startTime = call.argument<Int>("startTime")
                    val duration = call.argument<Int>("duration")
                    val segmentIndex = call.argument<Int>("segmentIndex")

                    if (audioPath.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "音频文件路径不能为空", null)
                        return
                    }

                    if (startTime == null || duration == null || segmentIndex == null) {
                        result.error("INVALID_ARGUMENT", "切割参数不能为空", null)
                        return
                    }

                    ffmpegHelper.splitAudio(
                        audioPath = audioPath,
                        startTime = startTime,
                        duration = duration,
                        segmentIndex = segmentIndex
                    ) { success, filePath, errorMessage ->
                        if (success) {
                            result.success(mapOf(
                                "success" to true,
                                "filePath" to filePath
                            ))
                        } else {
                            result.success(mapOf(
                                "success" to false,
                                "error" to errorMessage
                            ))
                        }
                    }
                }
                "extractFramesFromVideo" -> {
                    val videoPath = call.argument<String>("videoPath")
                    val outputDir = call.argument<String>("outputDir")

                    if (videoPath.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "视频路径不能为空", null)
                        return
                    }

                    ffmpegHelper.extractFramesFromVideo(
                        videoPath = videoPath,
                        outputDir = outputDir
                    ) { success, outputDirPath, errorMessage ->
                        val resultMap = mutableMapOf<String, Any?>()
                        resultMap["success"] = success
                        if (success) {
                            resultMap["outputDir"] = outputDirPath
                        } else {
                            resultMap["error"] = errorMessage
                        }
                        result.success(resultMap)
                    }
                }
                "downloadVideoAndExtractText" -> {
                    val url = call.argument<String>("url")
                    val userAgent = call.argument<String>("userAgent")
                    val referer = call.argument<String>("referer")
                    val useChinese = call.argument<Boolean>("useChinese") ?: true

                    if (url.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "视频URL不能为空", null)
                        return
                    }

                    ffmpegHelper.downloadVideoAndExtractText(
                        url = url,
                        userAgent = userAgent,
                        referer = referer,
                        useChinese = useChinese
                    ) { success, texts, errorMessage ->
                        val resultMap = mutableMapOf<String, Any?>()
                        resultMap["success"] = success
                        if (success && texts != null) {
                            resultMap["texts"] = texts
                        } else {
                            resultMap["error"] = errorMessage
                        }
                        result.success(resultMap)
                    }
                }
                "extractVideoUrlAndExtractText" -> {
                    val webpageUrl = call.argument<String>("webpageUrl")
                    val timeout = call.argument<Int>("timeout") ?: 30000
                    val useChinese = call.argument<Boolean>("useChinese") ?: true

                    if (webpageUrl.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "网页URL不能为空", null)
                        return
                    }

                    ffmpegHelper.extractVideoUrlAndExtractText(
                        webpageUrl = webpageUrl,
                        timeout = timeout.toLong(),
                        useChinese = useChinese
                    ) { success, texts, errorMessage ->
                        val resultMap = mutableMapOf<String, Any?>()
                        resultMap["success"] = success
                        if (success && texts != null) {
                            resultMap["texts"] = texts
                        } else {
                            resultMap["error"] = errorMessage
                        }
                        result.success(resultMap)
                    }
                }
                "downloadVideoAndExtractSubtitles" -> {
                    val url = call.argument<String>("url")
                    val userAgent = call.argument<String>("userAgent")
                    val referer = call.argument<String>("referer")
                    val useChinese = call.argument<Boolean>("useChinese") ?: true

                    if (url.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "视频URL不能为空", null)
                        return
                    }

                    Log.d(TAG, "开始OCR识别视频字幕: $url")

                    ffmpegHelper.downloadVideoAndExtractSubtitles(
                        url = url,
                        userAgent = userAgent,
                        referer = referer,
                        useChinese = useChinese
                    ) { success, subtitles, errorMessage ->
                        val resultMap = mutableMapOf<String, Any?>()
                        resultMap["success"] = success
                        if (success && subtitles != null) {
                            resultMap["subtitles"] = subtitles
                            resultMap["subtitleCount"] = subtitles.size
                            Log.d(TAG, "OCR识别视频字幕成功，共${subtitles.size}条")
                        } else {
                            resultMap["error"] = errorMessage
                            Log.e(TAG, "OCR识别视频字幕失败: $errorMessage")
                        }
                        result.success(resultMap)
                    }
                }

                // OCR相关方法 (PaddleOCR)
                "recognizeText" -> {
                    val imageUri = call.argument<String>("imageUri")
                    val useChinese = call.argument<Boolean>("useChinese") ?: true

                    if (imageUri.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "图片URI不能为空", null)
                        return
                    }

                    paddleOcrHelper.recognizeText(imageUri, useChinese, result)
                }
                "labelImage" -> {
                    val imageUri = call.argument<String>("imageUri")

                    if (imageUri.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "图片URI不能为空", null)
                        return
                    }

                    // 保留MLKit图像标签识别功能接口，实际功能可能不支持
                    result.error("UNSUPPORTED_OPERATION", "PaddleOCR SDK不支持图像标签识别", null)
                }
                "detectFaces" -> {
                    val imageUri = call.argument<String>("imageUri")

                    if (imageUri.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "图片URI不能为空", null)
                        return
                    }

                    // 保留MLKit人脸检测功能接口，实际功能可能不支持
                    result.error("UNSUPPORTED_OPERATION", "PaddleOCR SDK不支持人脸检测", null)
                }
                "scanBarcodes" -> {
                    val imageUri = call.argument<String>("imageUri")

                    if (imageUri.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "图片URI不能为空", null)
                        return
                    }

                    // 保留MLKit条码扫描功能接口，实际功能可能不支持
                    result.error("UNSUPPORTED_OPERATION", "PaddleOCR SDK不支持条码扫描", null)
                }

                // WebView相关方法
                "extractVideoUrlFromWebpage" -> {
                    val url = call.argument<String>("url")
                    val timeout = call.argument<Int>("timeout") ?: 30000

                    if (url.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "网页URL不能为空", null)
                        return
                    }

                    webViewHelper.extractVideoUrl(
                        url = url,
                        timeout = timeout.toLong(),
                        callback = { success: Boolean, resultData: Map<String, List<String>>?, errorMessage: String? ->
                            val resultMap = mutableMapOf<String, Any?>()
                            resultMap["success"] = success
                            if (success && resultData != null) {
                                // 从结果中获取视频URL
                                val videoUrls = resultData["video"]
                                if (!videoUrls.isNullOrEmpty()) {
                                    resultMap["videoUrl"] = videoUrls.first()
                                    resultMap["allVideoUrls"] = videoUrls
                                }
                                // 添加图片URL
                                val imageUrls = resultData["image"]
                                if (!imageUrls.isNullOrEmpty()) {
                                    resultMap["imageUrls"] = imageUrls
                                }
                            } else {
                                resultMap["error"] = errorMessage ?: "未知错误"
                            }
                            result.success(resultMap)
                        }
                    )
                }

                "openWebPage" -> {
                    val url = call.argument<String>("url")
                    val useSystemBrowser = call.argument<Boolean>("useSystemBrowser") ?: false

                    if (url.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "网页URL不能为空", null)
                        return
                    }

                    try {
                        webViewHelper.openWebPage(url, useSystemBrowser)
                        result.success(true)
                    } catch (e: Exception) {
                        Log.e(TAG, "打开网页时出错", e)
                        result.error("OPEN_WEBPAGE_ERROR", "打开网页时出错: ${e.message}", null)
                    }
                }

                "showToast" -> {
                    val message = call.argument<String>("message")
                    val duration = call.argument<Int>("duration") ?: Toast.LENGTH_SHORT

                    if (message.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "Toast消息不能为空", null)
                        return
                    }

                    try {
                        // 使用ToastHelper显示Toast
                        ToastHelper.show(context, message, duration)
                        result.success(true)
                    } catch (e: Exception) {
                        Log.e(TAG, "显示Toast时出错", e)
                        result.error("TOAST_ERROR", "显示Toast时出错: ${e.message}", null)
                    }
                }

                // SharedPreferences相关方法
                "putString" -> {
                    val key = call.argument<String>("key")
                    val value = call.argument<String>("value")

                    if (key.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "键名不能为空", null)
                        return
                    }

                    try {
                        Log.d(TAG, "Flutter调用putString: key=$key, value=${value ?: ""}")
                        sharedPreferencesHelper.putString(key, value ?: "")

                        // 验证是否成功保存
                        val savedValue = sharedPreferencesHelper.getString(key, "")
                        Log.d(TAG, "验证保存结果: key=$key, savedValue=$savedValue")

                        result.success(true)
                    } catch (e: Exception) {
                        Log.e(TAG, "存储字符串时出错", e)
                        result.error("STORAGE_ERROR", "存储字符串时出错: ${e.message}", null)
                    }
                }

                "getString" -> {
                    val key = call.argument<String>("key")
                    val defaultValue = call.argument<String>("defaultValue") ?: ""

                    if (key.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "键名不能为空", null)
                        return
                    }

                    try {
                        val value = sharedPreferencesHelper.getString(key, defaultValue)
                        result.success(value)
                    } catch (e: Exception) {
                        Log.e(TAG, "获取字符串时出错", e)
                        result.error("STORAGE_ERROR", "获取字符串时出错: ${e.message}", null)
                    }
                }

                // 自定义Toast相关方法
                "showCustomToast" -> {
                    val message = call.argument<String>("message") ?: ""
                    val duration = call.argument<Int>("duration") ?: CustomToastHelper.LENGTH_SHORT

                    if (checkOverlayPermission()) {
                        CustomToastHelper.showToast(context, message, duration)
                        result.success(true)
                    } else {
                        // 没有悬浮窗权限，使用系统Toast
                        Toast.makeText(context, message,
                            if (duration == CustomToastHelper.LENGTH_LONG) Toast.LENGTH_LONG else Toast.LENGTH_SHORT
                        ).show()
                        result.success(false)
                    }
                }

                // 自定义Toast Modal相关方法
                "showCustomToastModal" -> {
                    val title = call.argument<String>("title") ?: "确认删除"
                    val content = call.argument<String>("content") ?: "确认要删除这个收藏吗？"
                    val cancelText = call.argument<String>("cancelText") ?: "取消"
                    val confirmText = call.argument<String>("confirmText") ?: "确定"

                    if (checkOverlayPermission()) {
                        CustomToastModalHelper.showModal(
                            context = context,
                            title = title,
                            content = content,
                            cancelText = cancelText,
                            confirmText = confirmText,
                            callback = object : CustomToastModalHelper.ModalCallback {
                                override fun onConfirm() {
                                    // 通过MethodChannel回调到Flutter端
                                    Handler(Looper.getMainLooper()).post {
                                        <EMAIL>?.invokeMethod("onModalConfirm", null)
                                    }
                                }

                                override fun onCancel() {
                                    // 通过MethodChannel回调到Flutter端
                                    Handler(Looper.getMainLooper()).post {
                                        <EMAIL>?.invokeMethod("onModalCancel", null)
                                    }
                                }
                            }
                        )
                        result.success(true)
                    } else {
                        result.error("PERMISSION_DENIED", "需要悬浮窗权限才能显示Modal", null)
                    }
                }

                // 转换资源链接为OSS链接
                "convertToOssLink" -> {
                    // 转发给OssResourceBridge处理
                    OssResourceBridge.handleMethodCall(context, call, result)
                }

                // 上传文件到OSS
                "uploadFileToOss" -> {
                    // 转发给OssBridge处理
                    OssBridge.handleMethodCall(context, call, result)
                }

                // 提取视频文案（类似debug页面的测试进度显示下载功能）
                "extractVideoText" -> {
                    val videoUrl = call.argument<String>("videoUrl")

                    if (videoUrl.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "视频URL不能为空", null)
                        return
                    }

                    Log.d(TAG, "开始提取视频文案: $videoUrl")

                    // 使用FFmpegHelper的下载和提取文字功能
                    ffmpegHelper.downloadVideoAndExtractText(
                        url = videoUrl,
                        userAgent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36",
                        referer = "https://www.douyin.com",
                        useChinese = true
                    ) { success, texts, errorMessage ->
                        if (success && texts != null) {
                            Log.d(TAG, "视频文案提取成功，共${texts.size}条")
                            result.success(mapOf(
                                "success" to true,
                                "texts" to texts,
                                "textCount" to texts.size
                            ))
                        } else {
                            Log.e(TAG, "视频文案提取失败: $errorMessage")
                            result.success(mapOf(
                                "success" to false,
                                "error" to (errorMessage ?: "提取视频文案失败")
                            ))
                        }
                    }
                }

                // 配置相关方法
                "getConfig" -> {
                    // 返回所有配置项为Map
                    val configMap = mapOf(
                        "api_base_url" to ConfigHelper.getString("api_base_url"),
                        "app_env" to ConfigHelper.getString("app_env"),
                        "app_download_url" to ConfigHelper.getString("app_download_url"),
                        "baidu_search_api_key" to ConfigHelper.getString("baidu_search_api_key"),
                        "xunfei_APPID" to ConfigHelper.getString("xunfei_APPID"),
                        "xunfei_APISecret" to ConfigHelper.getString("xunfei_APISecret"),
                        "xunfei_APIKey" to ConfigHelper.getString("xunfei_APIKey"),
                        "tencent_appid" to ConfigHelper.getString("tencent_cloud_app_id"),
                        "tencent_cloud_secret_id" to ConfigHelper.getString("tencent_cloud_secret_id"),
                        "tencent_cloud_secret_key" to ConfigHelper.getString("tencent_cloud_secret_key")
                        // 可在此添加更多配置项
                    )
                    result.success(configMap)
                }

                // 获取版本信息
                "getVersionInfo" -> {
                    getVersionInfo(context, result)
                }

                // 返回按钮悬浮窗相关方法
                "showReturnButton" -> {
                    returnButtonHelper.show()
                    result.success(true)
                }

                "hideReturnButton" -> {
                    returnButtonHelper.hide()
                    result.success(true)
                }

                "isReturnButtonShowing" -> {
                    val isShowing = returnButtonHelper.isShowing()
                    result.success(isShowing)
                }

                // WebViewHtmlExtractor相关方法
                "extractHtmlFromUrl" -> {
                    val url = call.argument<String>("url")

                    if (url.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "URL不能为空", null)
                        return
                    }

                    WebViewHtmlExtractor.extractHtmlFromUrl(
                        context,
                        url
                    ) { html, error ->
                        val resultMap = mutableMapOf<String, Any?>()
                        if (html != null) {
                            resultMap["success"] = true
                            resultMap["html"] = html
                        } else {
                            resultMap["success"] = false
                            resultMap["error"] = error ?: "未知错误"
                        }
                        result.success(resultMap)
                    }
                }

                // 微信相关方法
                "isWechatInstalled" -> {
                    val isInstalled = wechatHelper.isWechatInstalled()
                    result.success(isInstalled)
                }
                "getWechatSupportedApi" -> {
                    val supportedApi = wechatHelper.getWechatSupportedApi()
                    result.success(supportedApi)
                }
                "openWechat" -> {
                    val success = wechatHelper.openWechat()
                    result.success(success)
                }
                "wechatLogin" -> {
                    val scope = call.argument<String>("scope") ?: "snsapi_userinfo"
                    val state = call.argument<String>("state") ?: "wechat_login"

                    if (!wechatHelper.isWechatInstalled()) {
                        result.success(mapOf(
                            "success" to false,
                            "error" to "微信未安装"
                        ))
                        return
                    }

                    val success = wechatHelper.login(scope, state)
                    result.success(mapOf(
                        "success" to success,
                        "error" to if (success) null else "发起微信登录失败"
                    ))
                }
                "wechatShareText" -> {
                    val text = call.argument<String>("text")
                    val scene = call.argument<Int>("scene") ?: 0 // 默认会话场景

                    if (text.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "分享文本不能为空", null)
                        return
                    }

                    if (!wechatHelper.isWechatInstalled()) {
                        result.success(mapOf(
                            "success" to false,
                            "error" to "微信未安装"
                        ))
                        return
                    }

                    val success = wechatHelper.shareText(text, scene)
                    result.success(mapOf(
                        "success" to success,
                        "error" to if (success) null else "发起微信分享失败"
                    ))
                }
                "wechatShareImage" -> {
                    val imagePath = call.argument<String>("imagePath")
                    val scene = call.argument<Int>("scene") ?: 0 // 默认会话场景

                    if (imagePath.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "图片路径不能为空", null)
                        return
                    }

                    if (!wechatHelper.isWechatInstalled()) {
                        result.success(mapOf(
                            "success" to false,
                            "error" to "微信未安装"
                        ))
                        return
                    }

                    val success = wechatHelper.shareImage(imagePath, scene)
                    result.success(mapOf(
                        "success" to success,
                        "error" to if (success) null else "发起微信分享失败"
                    ))
                }
                "wechatShareWebpage" -> {
                    val url = call.argument<String>("url")
                    val title = call.argument<String>("title")
                    val description = call.argument<String>("description")
                    val thumbImagePath = call.argument<String>("thumbImagePath")
                    val scene = call.argument<Int>("scene") ?: 0 // 默认会话场景

                    if (url.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "网页链接不能为空", null)
                        return
                    }

                    if (title.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "标题不能为空", null)
                        return
                    }

                    if (!wechatHelper.isWechatInstalled()) {
                        result.success(mapOf(
                            "success" to false,
                            "error" to "微信未安装"
                        ))
                        return
                    }

                    val success = wechatHelper.shareWebpage(url, title, description ?: "", thumbImagePath, scene)
                    result.success(mapOf(
                        "success" to success,
                        "error" to if (success) null else "发起微信分享失败"
                    ))
                }

                // 应用下载相关方法
                "downloadApp" -> {
                    val url = call.argument<String>("url")

                    if (url.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "下载地址不能为空", null)
                        return
                    }

                    appDownloadManager.downloadApp(url, object : AppDownloadManager.DownloadCallback {
                        override fun onSuccess(filePath: String) {
                            result.success(mapOf(
                                "success" to true,
                                "filePath" to filePath
                            ))
                        }

                        override fun onError(error: String) {
                            result.success(mapOf(
                                "success" to false,
                                "error" to error
                            ))
                        }
                    })
                }

                "installApp" -> {
                    val filePath = call.argument<String>("filePath")

                    if (filePath.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "文件路径不能为空", null)
                        return
                    }

                    val success = appDownloadManager.installApp(filePath)
                    result.success(success)
                }

                "cancelDownload" -> {
                    appDownloadManager.cancelDownload()
                    result.success(true)
                }

                "hasInstallPermission" -> {
                    val hasPermission = appDownloadManager.hasInstallPermission()
                    result.success(hasPermission)
                }

                "requestInstallPermission" -> {
                    appDownloadManager.requestInstallPermission()
                    result.success(true)
                }

                "getDownloadDirectory" -> {
                    val directory = appDownloadManager.getDownloadDirectory().absolutePath
                    result.success(directory)
                }

                "cleanDownloadCache" -> {
                    appDownloadManager.cleanDownloadCache()
                    result.success(true)
                }

                // 自定义通知面板相关方法
                "showCustomNotificationPanel" -> {
                    customNotificationPanel.showPanel()
                    result.success(true)
                }
                "hideCustomNotificationPanel" -> {
                    customNotificationPanel.hidePanel()
                    result.success(true)
                }
                "getCustomNotificationPanelSwitchState" -> {
                    val switchState = customNotificationPanel.getSwitchState()
                    result.success(switchState)
                }
                "checkNotificationPermission" -> {
                    val hasPermission = checkNotificationPermission()
                    result.success(hasPermission)
                }

                // 笔记WebView相关方法
                "openNoteWebview" -> {
                    val noteId = call.argument<String>("noteId")

                    Log.d(TAG, "Flutter调用openNoteWebview: noteId=$noteId")

                    noteWebviewHelper.openNoteWebview(
                        noteId = noteId
                    ) { success, error ->
                        val resultMap = mutableMapOf<String, Any?>()
                        resultMap["success"] = success
                        if (!success && error != null) {
                            resultMap["error"] = error
                        }
                        result.success(resultMap)
                    }
                }

                // 跳转到草稿编辑页面
                "openDraftEdit" -> {
                    val noteId = call.argument<String>("noteId")

                    Log.d(TAG, "原生调用openDraftEdit: noteId=$noteId")

                    if (noteId.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "笔记ID不能为空", null)
                        return
                    }

                    // 通过MethodChannel通知Flutter端跳转到草稿编辑页面
                    Handler(Looper.getMainLooper()).post {
                        <EMAIL>?.invokeMethod("navigateToDraftEdit", mapOf("noteId" to noteId))
                    }

                    result.success(true)
                }

                // 布尔值存储相关方法
                "putBool" -> {
                    val key = call.argument<String>("key")
                    val value = call.argument<Boolean>("value")

                    if (key.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "键名不能为空", null)
                        return
                    }

                    try {
                        Log.d(TAG, "Flutter调用putBool: key=$key, value=${value ?: false}")
                        sharedPreferencesHelper.putBoolean(key, value ?: false)

                        // 验证是否成功保存
                        val savedValue = sharedPreferencesHelper.getBoolean(key, false)
                        Log.d(TAG, "验证保存结果: key=$key, savedValue=$savedValue")

                        result.success(true)
                    } catch (e: Exception) {
                        Log.e(TAG, "存储布尔值时出错", e)
                        result.error("STORAGE_ERROR", "存储布尔值时出错: ${e.message}", null)
                    }
                }

                "getBool" -> {
                    val key = call.argument<String>("key")
                    val defaultValue = call.argument<Boolean>("defaultValue") ?: false

                    if (key.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "键名不能为空", null)
                        return
                    }

                    try {
                        val value = sharedPreferencesHelper.getBoolean(key, defaultValue)
                        Log.d(TAG, "Flutter调用getBool: key=$key, value=$value")
                        result.success(value)
                    } catch (e: Exception) {
                        Log.e(TAG, "获取布尔值时出错", e)
                        result.error("STORAGE_ERROR", "获取布尔值时出错: ${e.message}", null)
                    }
                }

                // SparkChain 大模型识别相关方法
                "initSparkChain" -> {
                    val appId = call.argument<String>("appId")
                    val apiKey = call.argument<String>("apiKey")
                    val apiSecret = call.argument<String>("apiSecret")
                    val logLevel = call.argument<Int>("logLevel") ?: 2
                    val logPath = call.argument<String>("logPath")
                    val uid = call.argument<String>("uid")

                    if (appId.isNullOrEmpty() || apiKey.isNullOrEmpty() || apiSecret.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "appId、apiKey、apiSecret不能为空", null)
                        return
                    }

                    val success = sparkChainHelper.initializeSDK(
                        appId = appId,
                        apiKey = apiKey,
                        apiSecret = apiSecret,
                        logLevel = logLevel,
                        logPath = logPath,
                        uid = uid
                    )

                    result.success(mapOf(
                        "success" to success,
                        "message" to if (success) "SparkChain SDK初始化成功" else "SparkChain SDK初始化失败"
                    ))
                }

                "isSparkChainInitialized" -> {
                    val isInitialized = sparkChainHelper.isSDKInitialized()
                    result.success(isInitialized)
                }

                "startSparkChainRecognition" -> {
                    val sessionId = call.argument<String>("sessionId") ?: "default"
                    val language = call.argument<String>("language") ?: "zh_cn"
                    val domain = call.argument<String>("domain") ?: "slm"
                    val accent = call.argument<String>("accent") ?: "mandarin"
                    val sampleRate = call.argument<Int>("sampleRate") ?: 16000
                    val encoding = call.argument<String>("encoding") ?: "raw"
                    val channels = call.argument<Int>("channels") ?: 1
                    val bitdepth = call.argument<Int>("bitdepth") ?: 16
                    val frameSize = call.argument<Int>("frameSize") ?: 0

                    // 配置参数
                    val vgap = call.argument<Int>("vgap")
                    val vadEos = call.argument<Int>("vadEos")
                    val vinfo = call.argument<Boolean>("vinfo")
                    val dwa = call.argument<String>("dwa")
                    val ptt = call.argument<Boolean>("ptt")
                    val smth = call.argument<Boolean>("smth")
                    val nunum = call.argument<Boolean>("nunum")
                    val rlang = call.argument<String>("rlang")
                    val ln = call.argument<String>("ln")

                    try {
                        // 创建ASR实例
                        val asr = sparkChainHelper.createASR(language, domain, accent)
                        if (asr == null) {
                            result.error("ASR_CREATE_ERROR", "创建ASR实例失败", null)
                            return
                        }

                        // 配置ASR参数
                        sparkChainHelper.configureASR(
                            asr = asr,
                            vgap = vgap,
                            vadEos = vadEos,
                            vinfo = vinfo,
                            dwa = dwa,
                            ptt = ptt,
                            smth = smth,
                            nunum = nunum,
                            rlang = rlang,
                            ln = ln
                        )

                        // 注册回调
                        sparkChainHelper.registerCallback(asr, sessionId, result)

                        // 启动识别
                        val success = sparkChainHelper.startRecognition(
                            asr = asr,
                            sampleRate = sampleRate,
                            encoding = encoding,
                            channels = channels,
                            bitdepth = bitdepth,
                            frameSize = frameSize
                        )

                        if (!success) {
                            result.error("RECOGNITION_START_ERROR", "启动识别失败", null)
                        }
                        // 注意：成功的结果会通过回调返回
                    } catch (e: Exception) {
                        Log.e(TAG, "启动SparkChain识别失败", e)
                        result.error("RECOGNITION_ERROR", "启动识别异常: ${e.message}", null)
                    }
                }

                "writeSparkChainAudioData" -> {
                    val audioData = call.argument<ByteArray>("audioData")

                    if (audioData == null) {
                        result.error("INVALID_ARGUMENT", "音频数据不能为空", null)
                        return
                    }

                    val success = sparkChainHelper.writeAudioData(audioData)
                    result.success(mapOf(
                        "success" to success,
                        "message" to if (success) "音频数据发送成功" else "音频数据发送失败"
                    ))
                }

                "stopSparkChainRecognition" -> {
                    val immediate = call.argument<Boolean>("immediate") ?: false

                    val success = sparkChainHelper.stopRecognition(immediate)
                    result.success(mapOf(
                        "success" to success,
                        "message" to if (success) "识别停止成功" else "识别停止失败"
                    ))
                }

                "recognizeAudioFile" -> {
                    val audioFilePath = call.argument<String>("audioFilePath")
                    val language = call.argument<String>("language") ?: "zh_cn"
                    val domain = call.argument<String>("domain") ?: "slm"
                    val accent = call.argument<String>("accent") ?: "mandarin"

                    if (audioFilePath == null) {
                        result.error("INVALID_ARGUMENT", "音频文件路径不能为空", null)
                        return
                    }

                    // 检查文件格式，如果不是PCM格式，先转换为PCM
                    val audioFile = File(audioFilePath)
                    val fileExtension = audioFile.extension.lowercase()

                    if (fileExtension == "pcm") {
                        // 直接识别PCM文件
                        sparkChainHelper.recognizeAudioFile(
                            audioFilePath = audioFilePath,
                            language = language,
                            domain = domain,
                            accent = accent
                        ) { success, recognitionResult, error ->
                            if (success) {
                                result.success(mapOf(
                                    "success" to true,
                                    "text" to recognitionResult,
                                    "message" to "音频识别成功"
                                ))
                            } else {
                                result.success(mapOf(
                                    "success" to false,
                                    "error" to error,
                                    "message" to "音频识别失败"
                                ))
                            }
                        }
                    } else {
                        // 先转换为PCM格式，再进行识别
                        ffmpegHelper.convertAudioToPCM(
                            audioPath = audioFilePath,
                            outputPath = null, // 使用默认路径
                            sampleRate = 16000,
                            channels = 1
                        ) { convertSuccess: Boolean, pcmFilePath: String?, duration: Double?, convertError: String? ->
                            if (!convertSuccess || pcmFilePath == null) {
                                result.success(mapOf(
                                    "success" to false,
                                    "error" to "音频格式转换失败: $convertError",
                                    "message" to "音频识别失败"
                                ))
                                return@convertAudioToPCM
                            }

                            // 识别转换后的PCM文件
                            sparkChainHelper.recognizeAudioFile(
                                audioFilePath = pcmFilePath,
                                language = language,
                                domain = domain,
                                accent = accent
                            ) { success, recognitionResult, error ->
                                // 识别完成后删除临时PCM文件
                                try {
                                    val pcmFile = File(pcmFilePath)
                                    if (pcmFile.exists()) {
                                        pcmFile.delete()
                                        Log.d(TAG, "已删除临时PCM文件: $pcmFilePath")
                                    }
                                } catch (e: Exception) {
                                    Log.w(TAG, "删除临时PCM文件失败: $e")
                                }

                                if (success) {
                                    result.success(mapOf(
                                        "success" to true,
                                        "text" to recognitionResult,
                                        "message" to "音频识别成功"
                                    ))
                                } else {
                                    result.success(mapOf(
                                        "success" to false,
                                        "error" to error,
                                        "message" to "音频识别失败"
                                    ))
                                }
                            }
                        }
                    }
                }

                "recognizeAudioFileWithProgress" -> {
                    val audioFilePath = call.argument<String>("audioFilePath")
                    val language = call.argument<String>("language") ?: "zh_cn"
                    val domain = call.argument<String>("domain") ?: "slm"
                    val accent = call.argument<String>("accent") ?: "mandarin"
                    val sessionId = call.argument<String>("sessionId") ?: "default_session"

                    if (audioFilePath == null) {
                        result.error("INVALID_ARGUMENT", "音频文件路径不能为空", null)
                        return
                    }

                    // 检查文件格式，只支持PCM格式的进度回调
                    val audioFile = File(audioFilePath)
                    val fileExtension = audioFile.extension.lowercase()

                    if (fileExtension == "pcm") {
                        // 直接识别PCM文件（支持进度回调）
                        sparkChainHelper.recognizeAudioFileWithProgress(
                            audioFilePath = audioFilePath,
                            language = language,
                            domain = domain,
                            accent = accent,
                            progressCallback = { progress ->
                                // 通过EventChannel发送进度更新，需要在主线程中执行
                                android.os.Handler(android.os.Looper.getMainLooper()).post {
                                    progressEventSink?.success(mapOf(
                                        "sessionId" to sessionId,
                                        "progress" to progress,
                                        "type" to "recognition_progress"
                                    ))
                                }
                            }
                        ) { success, recognitionResult, error ->
                            if (success) {
                                result.success(mapOf(
                                    "success" to true,
                                    "text" to recognitionResult,
                                    "message" to "音频识别成功"
                                ))
                            } else {
                                result.success(mapOf(
                                    "success" to false,
                                    "error" to error,
                                    "message" to "音频识别失败"
                                ))
                            }
                        }
                    } else {
                        result.success(mapOf(
                            "success" to false,
                            "error" to "暂不支持非PCM格式的进度回调识别",
                            "message" to "格式不支持"
                        ))
                    }
                }

                "isSparkChainRecognitionActive" -> {
                    val isActive = sparkChainHelper.isRecognitionActive()
                    result.success(isActive)
                }

                "uninitSparkChain" -> {
                    sparkChainHelper.uninitializeSDK()
                    result.success(mapOf(
                        "success" to true,
                        "message" to "SparkChain SDK逆初始化完成"
                    ))
                }
                "convertAudioToPCM" -> {
                    val audioPath = call.argument<String>("audioPath")
                    val sampleRate = call.argument<Int>("sampleRate") ?: 16000
                    val channels = call.argument<Int>("channels") ?: 1
                    val saveToGallery = call.argument<Boolean>("saveToGallery") ?: false

                    if (audioPath.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "音频文件路径不能为空", null)
                        return
                    }

                    // 根据saveToGallery参数决定输出路径
                    val outputPath = if (saveToGallery) {
                        // 保存到下载目录
                        val originalFile = File(audioPath)
                        val nameWithoutExtension = originalFile.nameWithoutExtension
                        val timestamp = System.currentTimeMillis()
                        "/storage/emulated/0/Download/${nameWithoutExtension}_${timestamp}.pcm"
                    } else {
                        // 使用默认缓存路径（传null让方法自动生成）
                        null
                    }

                    ffmpegHelper.convertAudioToPCM(
                        audioPath = audioPath,
                        outputPath = outputPath,
                        sampleRate = sampleRate,
                        channels = channels
                    ) { success, filePath, duration, errorMessage ->
                        if (success) {
                            val resultMap = mutableMapOf<String, Any?>(
                                "success" to true,
                                "filePath" to filePath
                            )
                            // 如果获取到了时长信息，则添加到结果中
                            if (duration != null) {
                                resultMap["duration"] = duration
                            }
                            result.success(resultMap)
                        } else {
                            result.success(mapOf(
                                "success" to false,
                                "error" to errorMessage
                            ))
                        }
                    }
                }

                // 腾讯云ASR SDK相关方法
                "initTencentAsrSdk" -> {
                    try {
                        // 从配置文件中读取腾讯云API密钥
                        val appId = ConfigHelper.getString("tencent_cloud_app_id")
                        val secretId = ConfigHelper.getString("tencent_cloud_secret_id")
                        val secretKey = ConfigHelper.getString("tencent_cloud_secret_key")

                        if (appId.isEmpty() || secretId.isEmpty() || secretKey.isEmpty()) {
                            result.success(mapOf(
                                "success" to false,
                                "error" to "配置文件中未找到腾讯云API密钥，请检查tencent_cloud_app_id、tencent_cloud_secret_id和tencent_cloud_secret_key配置项"
                            ))
                            return
                        }

                        val config = TencentAsrHelper.TencentCloudConfig(appId, secretId, secretKey)
                        val success = tencentAsrHelper.initializeConfig(config)

                        if (success) {
                            Log.i(TAG, "腾讯云ASR SDK初始化成功，AppId: ${appId.take(8)}...")
                            result.success(mapOf(
                                "success" to true,
                                "message" to "腾讯云ASR SDK初始化成功"
                            ))
                        } else {
                            result.success(mapOf(
                                "success" to false,
                                "error" to "腾讯云ASR SDK初始化失败"
                            ))
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "腾讯云ASR SDK初始化失败", e)
                        result.success(mapOf(
                            "success" to false,
                            "error" to "初始化失败: ${e.message}"
                        ))
                    }
                }

                "recognizeAudioByData" -> {
                    val audioData = call.argument<ByteArray>("audioData")
                    val voiceFormat = call.argument<String>("voiceFormat") ?: "mp3"
                    val engineModelType = call.argument<String>("engineModelType") ?: "16k_zh"
                    val recognitionType = call.argument<String>("recognitionType") ?: "oneSentence"

                    if (audioData == null) {
                        result.error("INVALID_ARGUMENT", "音频数据不能为空", null)
                        return
                    }

                    if (!tencentAsrHelper.isConfigured()) {
                        result.success(mapOf(
                            "success" to false,
                            "error" to "腾讯云ASR SDK未初始化，请先调用initTencentAsrSdk"
                        ))
                        return
                    }

                    val callback = object : TencentAsrHelper.RecognitionCallback {
                        override fun onSuccess(recognitionResult: String) {
                            Log.i(TAG, "腾讯云ASR识别成功")
                            result.success(mapOf(
                                "success" to true,
                                "result" to recognitionResult,
                                "message" to "语音识别成功"
                            ))
                        }

                        override fun onError(error: String) {
                            Log.e(TAG, "腾讯云ASR识别失败: $error")
                            result.success(mapOf(
                                "success" to false,
                                "error" to error
                            ))
                        }
                    }

                    try {
                        when (recognitionType) {
                            "oneSentence" -> {
                                tencentAsrHelper.recognizeOneSentenceByData(audioData, voiceFormat, engineModelType, callback)
                            }
                            "flash" -> {
                                tencentAsrHelper.recognizeFlashByData(audioData, voiceFormat, engineModelType, callback)
                            }
                            else -> {
                                result.error("INVALID_ARGUMENT", "不支持的识别类型: $recognitionType", null)
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "腾讯云ASR识别异常", e)
                        result.success(mapOf(
                            "success" to false,
                            "error" to "识别异常: ${e.message}"
                        ))
                    }
                }

                "recognizeAudioByFile" -> {
                    val filePath = call.argument<String>("filePath")
                    val voiceFormat = call.argument<String>("voiceFormat") ?: "mp3"
                    val engineModelType = call.argument<String>("engineModelType") ?: "16k_zh"
                    val recognitionType = call.argument<String>("recognitionType") ?: "oneSentence"

                    if (filePath.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "文件路径不能为空", null)
                        return
                    }

                    if (!tencentAsrHelper.isConfigured()) {
                        result.success(mapOf(
                            "success" to false,
                            "error" to "腾讯云ASR SDK未初始化，请先调用initTencentAsrSdk"
                        ))
                        return
                    }

                    val callback = object : TencentAsrHelper.RecognitionCallback {
                        override fun onSuccess(recognitionResult: String) {
                            Log.i(TAG, "腾讯云ASR文件识别成功")
                            result.success(mapOf(
                                "success" to true,
                                "result" to recognitionResult,
                                "message" to "语音识别成功"
                            ))
                        }

                        override fun onError(error: String) {
                            Log.e(TAG, "腾讯云ASR文件识别失败: $error")
                            result.success(mapOf(
                                "success" to false,
                                "error" to error
                            ))
                        }
                    }

                    try {
                        // 检查文件扩展名，如果是MP4文件，使用专门的方法
                        val fileExtension = filePath.substringAfterLast('.', "").lowercase()
                        val isVideoFile = fileExtension in listOf("mp4", "mov", "avi", "mkv", "flv", "wmv", "webm")

                        when (recognitionType) {
                            "oneSentence" -> {
                                if (isVideoFile) {
                                    // 对于视频文件，暂不支持一句话识别，建议使用flash识别
                                    result.success(mapOf(
                                        "success" to false,
                                        "error" to "视频文件不支持一句话识别，请使用flash识别类型"
                                    ))
                                } else {
                                    tencentAsrHelper.recognizeOneSentenceByFile(filePath, voiceFormat, engineModelType, callback)
                                }
                            }
                            "flash" -> {
                                if (isVideoFile) {
                                    // 对于视频文件，使用专门的MP4识别方法
                                    tencentAsrHelper.recognizeFlashByMp4File(filePath, engineModelType, callback)
                                } else {
                                    tencentAsrHelper.recognizeFlashByFile(filePath, voiceFormat, engineModelType, callback)
                                }
                            }
                            else -> {
                                result.error("INVALID_ARGUMENT", "不支持的识别类型: $recognitionType", null)
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "腾讯云ASR文件识别异常", e)
                        result.success(mapOf(
                            "success" to false,
                            "error" to "识别异常: ${e.message}"
                        ))
                    }
                }

                "executeBusinessJavaScript" -> {
                    val url = call.argument<String>("url")
                    val businessName = call.argument<String>("businessName")
                    val replacements = call.argument<Map<String, String>>("replacements") ?: emptyMap()

                    if (url.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "URL不能为空", null)
                        return
                    }

                    if (businessName.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "业务名称不能为空", null)
                        return
                    }

                    WebViewHtmlExtractor.executeBusinessJavaScript(
                        context,
                        url,
                        businessName,
                        replacements
                    ) { jsResult, error ->
                        val resultMap = mutableMapOf<String, Any?>()
                        if (jsResult != null) {
                            resultMap["success"] = true
                            resultMap["result"] = jsResult
                        } else {
                            resultMap["success"] = false
                            resultMap["error"] = error ?: "未知错误"
                        }
                        result.success(resultMap)
                    }
                }

                // 笔记相关方法
                "createNote" -> {
                    val title = call.argument<String>("title")
                    val content = call.argument<String>("content")
                    val parent_id = call.argument<String>("parent_id")
                    val cover = call.argument<String>("cover")
                    val desc = call.argument<String>("desc")
                    val html = call.argument<String>("html")

                    if (title.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "笔记标题不能为空", null)
                        return
                    }

                    if (content.isNullOrEmpty()) {
                        result.error("INVALID_ARGUMENT", "笔记内容不能为空", null)
                        return
                    }

                    Log.d(TAG, "Flutter调用createNote: title=$title")

                    val noteApi = NoteApi()
                    noteApi.createNote(
                        context = context,
                        title = title,
                        content = content,
                        parent_id = parent_id,
                        cover = cover,
                        desc = desc,
                        html = html
                    ) { success, noteId, error ->
                        val resultMap = mutableMapOf<String, Any?>()
                        resultMap["success"] = success
                        if (success && noteId != null) {
                            resultMap["noteId"] = noteId
                            resultMap["message"] = "笔记创建成功"
                        } else {
                            resultMap["error"] = error ?: "创建笔记失败"
                        }
                        result.success(resultMap)
                    }
                }

                // 未知方法
                else -> {
                    result.notImplemented()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理方法调用时出错", e)
            result.error("NATIVE_ERROR", "原生代码执行出错: ${e.message}", null)
        }
    }

    /**
     * 检查通知权限
     */
    private fun checkNotificationPermission(): Boolean {
        return try {
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                // Android 13+ 需要检查通知权限
                val hasPermission = notificationManager.areNotificationsEnabled()
                Log.d(TAG, "通知权限检查结果: $hasPermission")
                hasPermission
            } else {
                // Android 13以下默认有通知权限
                Log.d(TAG, "Android版本低于13，默认有通知权限")
                true
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查通知权限失败", e)
            false
        }
    }

    /**
     * 获取应用版本信息
     */
    private fun getVersionInfo(context: Context, result: MethodChannel.Result) {
        try {
            val packageManager = context.packageManager
            val packageInfo = packageManager.getPackageInfo(context.packageName, 0)

            val versionMap = mapOf(
                "versionName" to packageInfo.versionName,
                "versionCode" to packageInfo.versionCode.toString(),
                "packageName" to packageInfo.packageName
            )

            Log.d(TAG, "获取版本信息成功: $versionMap")
            result.success(versionMap)
        } catch (e: Exception) {
            Log.e(TAG, "获取版本信息失败", e)
            result.error("VERSION_ERROR", "获取版本信息失败: ${e.message}", null)
        }
    }
}

/**
 * 剪贴板事件处理器
 * 用于向Flutter发送剪贴板变化事件
 */
class ClipboardEventHandler(private val context: Context) : EventChannel.StreamHandler {
    private var eventSink: EventChannel.EventSink? = null

    override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
        eventSink = events
        ClipboardHelper.getInstance(context).setOnClipboardChangedListener { text ->
            eventSink?.success(text)
        }
    }

    override fun onCancel(arguments: Any?) {
        eventSink = null
        ClipboardHelper.getInstance(context).setOnClipboardChangedListener(null)
    }
}

/**
 * 悬浮窗事件处理器
 * 用于向Flutter发送悬浮窗状态变化事件
 */
class FloatingWindowEventHandler(private val context: Context) : EventChannel.StreamHandler {
    private var eventSink: EventChannel.EventSink? = null
    private val broadcastReceiver = object : android.content.BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == "com.xunhe.aishoucang.FLOATING_WINDOW_CLOSED") {
                eventSink?.success("closed")
                Log.d("FloatingWindowEvent", "发送悬浮窗关闭事件到Flutter")
            }
        }
    }

    override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
        eventSink = events
        // 注册广播接收器
        val filter = android.content.IntentFilter("com.xunhe.aishoucang.FLOATING_WINDOW_CLOSED")

        // 使用工具类安全注册广播接收器
        com.xunhe.aishoucang.lib.BroadcastReceiverUtils.registerInternalReceiver(
            context = context,
            receiver = broadcastReceiver,
            filter = filter,
            logTag = "FloatingWindowEvent"
        )
        Log.d("FloatingWindowEvent", "注册悬浮窗关闭事件广播接收器")
    }

    override fun onCancel(arguments: Any?) {
        eventSink = null
        // 取消注册广播接收器
        com.xunhe.aishoucang.lib.BroadcastReceiverUtils.safeUnregisterReceiver(
            context = context,
            receiver = broadcastReceiver,
            logTag = "FloatingWindowEvent"
        )
        Log.d("FloatingWindowEvent", "取消注册悬浮窗关闭事件广播接收器")
    }
}

/**
 * 微信事件处理器
 * 用于向Flutter发送微信登录和分享回调事件
 */
class WechatEventHandler(private val context: Context) : EventChannel.StreamHandler, WechatAuthListener, WechatShareListener {
    private var eventSink: EventChannel.EventSink? = null
    private val TAG = "WechatEventHandler"

    override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
        eventSink = events
        // 注册微信回调监听器
        val wechatHelper = WechatHelper.getInstance(context)
        wechatHelper.addAuthListener(this)
        wechatHelper.addShareListener(this)
        Log.d(TAG, "注册微信事件监听器")
    }

    override fun onCancel(arguments: Any?) {
        eventSink = null
        // 取消注册微信回调监听器
        val wechatHelper = WechatHelper.getInstance(context)
        wechatHelper.removeAuthListener(this)
        wechatHelper.removeShareListener(this)
        Log.d(TAG, "取消注册微信事件监听器")
    }

    // WechatAuthListener接口实现
    override fun onAuthSuccess(code: String) {
        Log.d(TAG, "微信授权成功，发送事件到Flutter")
        Log.d(TAG, "发送到Flutter的授权码: $code")

        // 创建要发送的Map
        val eventMap = mapOf(
            "type" to "auth",
            "result" to "success",
            "code" to code
        )

        // 打印完整的Map内容
        Log.d(TAG, "发送到Flutter的完整事件Map: $eventMap")

        // 发送事件
        eventSink?.success(eventMap)
    }

    override fun onAuthCancel() {
        Log.d(TAG, "微信授权取消，发送事件到Flutter")
        eventSink?.success(mapOf(
            "type" to "auth",
            "result" to "cancel"
        ))
    }

    override fun onAuthDenied() {
        Log.d(TAG, "微信授权拒绝，发送事件到Flutter")
        eventSink?.success(mapOf(
            "type" to "auth",
            "result" to "denied"
        ))
    }

    override fun onAuthFail(errCode: Int, errMsg: String) {
        Log.d(TAG, "微信授权失败，发送事件到Flutter")
        eventSink?.success(mapOf(
            "type" to "auth",
            "result" to "fail",
            "errCode" to errCode,
            "errMsg" to errMsg
        ))
    }

    // WechatShareListener接口实现
    override fun onShareSuccess() {
        Log.d(TAG, "微信分享成功，发送事件到Flutter")
        eventSink?.success(mapOf(
            "type" to "share",
            "result" to "success"
        ))
    }

    override fun onShareCancel() {
        Log.d(TAG, "微信分享取消，发送事件到Flutter")
        eventSink?.success(mapOf(
            "type" to "share",
            "result" to "cancel"
        ))
    }

    override fun onShareFail(errCode: Int, errMsg: String) {
        Log.d(TAG, "微信分享失败，发送事件到Flutter")
        eventSink?.success(mapOf(
            "type" to "share",
            "result" to "fail",
            "errCode" to errCode,
            "errMsg" to errMsg
        ))
    }
}

/**
 * 进度事件处理器
 * 用于向Flutter发送进度更新事件
 */
class ProgressEventHandler : EventChannel.StreamHandler {
    private val TAG = "ProgressEventHandler"

    override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
        FlutterBridge.progressEventSink = events
        Log.d(TAG, "注册进度事件监听器")
    }

    override fun onCancel(arguments: Any?) {
        FlutterBridge.progressEventSink = null
        Log.d(TAG, "取消注册进度事件监听器")
    }
}

